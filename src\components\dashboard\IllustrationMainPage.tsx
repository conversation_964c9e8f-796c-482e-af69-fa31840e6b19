import React, { useState, useMemo } from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import AsIsPage from './AsIsPage';
import FaceAmountPage from './FaceAmountPage';
import PremiumPage from './PremiumPage';
import IncomePage from './IncomePage';
import LoanRepaymentPage from './LoanRepaymentPage';
import InterestRatePage from './InterestRatePage';
import Card from '../common/Card';
import Button from '../common/Button';
import { Calculator, ChevronDown, ChevronRight, Bookmark, DollarSign, TrendingUp, Download, Percent } from 'lucide-react';
import SelectedScenariosPage from './SelectedScenariosPage';
import IllustrationTypeDebugger from '../debug/IllustrationTypeDebugger';

interface IllustrationTab {
  id: string;
  typeId: number; // Unique ID from 1 to 6 for backend filtering
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const IllustrationMainPage: React.FC = () => {
  const { activeTab, setActiveTab, selectedCustomerData, selectedPolicyData, scenarios, allowedIllustrationTypes, error } = useDashboard();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const illustrationTabs: IllustrationTab[] = [
    {
      id: 'as-is',
      typeId: 1,
      label: 'As-Is',
      icon: Bookmark,
      description: 'Current policy illustration'
    },
    {
      id: 'face-amount',
      typeId: 2,
      label: 'Face Amount',
      icon: DollarSign,
      description: 'Death benefit amount scenarios'
    },
    {
      id: 'premium',
      typeId: 3,
      label: 'Premium',
      icon: TrendingUp,
      description: 'Premium payment scenarios'
    },
    {
      id: 'interest-rate',
      typeId: 4,
      label: 'Interest Rate',
      icon: Percent,
      description: 'Interest rate based scenarios'
    },
    {
      id: 'income',
      typeId: 5,
      label: 'FULL SURRENDER / INCOME (LOAN & WITHDRAWAL)',
      icon: Download,
      description: 'Loan & withdrawal scenarios'
    },
    {
      id: 'loan-repayment',
      typeId: 6,
      label: 'Loan Repayment',
      icon: Percent,
      description: 'Loan repayment scenarios'
    }
  ];

  // 🔥 BACKEND FILTERING: Filter illustration tabs based on backend response
  const filteredIllustrationTabs = useMemo(() => {
    console.log('🔍 FILTERING - allowedTypes:', allowedIllustrationTypes, 'totalTabs:', illustrationTabs.length);

    // Only filter if backend returned specific allowed types
    if (!allowedIllustrationTypes || allowedIllustrationTypes.length === 0) {
      console.log('  ❌ No filtering - showing all tabs');
      return illustrationTabs;
    }

    // Filter based on backend response
    const filtered = illustrationTabs.filter(tab => allowedIllustrationTypes.includes(tab.typeId));
    console.log('  ✅ Filtered to', filtered.length, 'tabs:', filtered.map(t => t.typeId));
    return filtered;
  }, [allowedIllustrationTypes]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    // Toggle the section when clicking on a tab
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(tabId)) {
      newExpanded.delete(tabId);
    } else {
      newExpanded.add(tabId);
    }
    setExpandedSections(newExpanded);
  };

  const getScenariosForCategory = (category: string) => {
    return scenarios.filter(scenario => scenario.category === category);
  };

  const renderIllustrationContent = () => {
    switch (activeTab) {
      case 'selected-scenarios':
        return <SelectedScenariosPage />;
      case 'as-is':
        return <AsIsPage />;
      case 'face-amount':
        return <FaceAmountPage />;
      case 'premium':
        return <PremiumPage />;
      case 'income':
        return <IncomePage />;
      case 'loan-repayment':
        return <LoanRepaymentPage />;
      case 'interest-rate':
        return <InterestRatePage />;
      case 'debug-types':
        return <IllustrationTypeDebugger />;
      default:
        return <AsIsPage />;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-[#121212]">
      {/* Current Policy Information at the top */}
      {selectedCustomerData && selectedPolicyData && (
        <div className="space-y-6 px-6">
          {/* Current Policy Information Card - Matching Image Structure */}
          <Card className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center">
                <Calculator className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Current Policy Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* First Row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Number</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {selectedCustomerData?.policyNumber || selectedCustomerData?.details?.["Policy Number"] || '1'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer Name</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {selectedCustomerData?.name || 'Sophia A. Miller'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer ID</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {selectedCustomerData?.customerId || selectedCustomerData?.details?.["Customer ID"] || '1'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Date of Birth</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {selectedCustomerData?.details?.DOB || '1990-05-15'}
                  </p>
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Type</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {selectedPolicyData?.name || selectedCustomerData?.details?.["Policy Type"] || 'Whole Life'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Face Amount</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    $ {selectedPolicyData?.coverage ?
                      parseInt(selectedPolicyData.coverage.replace(/[,$]/g, '').match(/(\d+)/)?.[1] || '0').toLocaleString() :
                      '250,000'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Annual Premium</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    $ {selectedPolicyData?.premium ?
                      parseInt(selectedPolicyData.premium.replace(/[,$]/g, '').replace(/annually/i, '').trim().match(/(\d+)/)?.[1] || '0').toLocaleString() :
                      '1,500'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Current Policy Year</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {(() => {
                      // Calculate policy year based on issue date from policy data
                      const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
                      if (issueDate) {
                        const issue = new Date(issueDate);
                        const today = new Date();
                        const yearsDiff = today.getFullYear() - issue.getFullYear();
                        const monthsDiff = today.getMonth() - issue.getMonth();
                        const daysDiff = today.getDate() - issue.getDate();

                        // Calculate total months more accurately
                        let totalMonths = yearsDiff * 12 + monthsDiff;
                        if (daysDiff >= 0) {
                          totalMonths += 1; // Add current month if we've passed the issue day
                        }

                        const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
                        return `Year ${policyYear}`;
                      }
                      return 'Year 6'; // Default fallback
                    })()}
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
      
      {/* Vertical Dropdown Navigation - Full Width */}
      {!selectedPolicyData && (
        <div className="px-4 pt-4">
          <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">Policy Selection Required</h3>
                <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                  Please select a policy first to view your selected scenarios and illustrations.
                </p>
                <Button
                  variant="primary"
                  onClick={() => setActiveTab('policy-selection')}
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      <div className="flex-1 px-4 pt-4">
        {/* Error Display */}
        {error && error.includes('illustration types') && (
          <div className="mb-4">
            <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">⚠</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">Illustration Types Loading Issue</h3>
                  <p className="text-yellow-700 dark:text-yellow-300">
                    {error} - Showing all illustration types as fallback.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Illustration Types Filter Info */}
        {filteredIllustrationTabs.length < illustrationTabs.length && !error && (
          <div className="mb-4">
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-400 text-lg font-bold">ℹ</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">Filtered Illustration Types</h3>
                  <p className="text-blue-700 dark:text-blue-300">
                    Showing {filteredIllustrationTabs.length} of {illustrationTabs.length} illustration types based on your policy configuration.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        <div className="space-y-4">
          {filteredIllustrationTabs.map((tab) => {
            const Icon = tab.icon;
            const isExpanded = expandedSections.has(tab.id);
            const isActive = activeTab === tab.id;
            const tabScenarios = getScenariosForCategory(tab.id);

            return (
              <div key={tab.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                {/* Dropdown Header */}
                <button
                  onClick={() => handleTabClick(tab.id)}
                  className={`w-full flex items-center justify-between p-6 text-left transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg ${
                    isExpanded ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-lg ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{tab.label}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{tab.description}</p>
                      {tabScenarios.length > 0 && tab.id !== 'face-amount' && tab.id !== 'loan-repayment' && (
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                          {tabScenarios.length} scenario{tabScenarios.length !== 1 ? 's' : ''} available
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isExpanded ? (
                      <ChevronDown className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                    )}
                  </div>
                </button>

                {/* Dropdown Content */}
                {isExpanded && (
                  <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                    <div className="p-6">
                      {/* Scenarios List - Hide for Face Amount and Loan Repayment tabs since they use database storage */}
                      {tabScenarios.length > 0 && tab.id !== 'face-amount' && tab.id !== 'loan-repayment' && (
                        <div className="mb-6">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Available Scenarios</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {tabScenarios.map((scenario) => (
                              <div
                                key={scenario.id}
                                className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 cursor-pointer"
                              >
                                <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">{scenario.name}</h5>
                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{scenario.asIsDetails}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Show Content Directly Under Tab When Active */}
                      {isActive && (
                        <div>
                          {renderIllustrationContent()}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default IllustrationMainPage;
