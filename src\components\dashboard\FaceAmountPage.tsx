import React, { useState, useEffect } from 'react';
import { Save, RotateCcw } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';
import { saveFaceAmountIllustration, validateFaceAmountData, FaceAmountIllustrationData } from '../../services/faceAmountService';

const FaceAmountPage = () => {
  // Add dashboard context for policy info
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario } = useDashboard();

  const [faceAmountData, setFaceAmountData] = useState({
    current_death_benefit: 0, // Will be updated from selected policy data
    current_death_benefit_option: 'A', // A = Level, B = Increasing
    want_to_change: false,
    change_immediately: false,
    change_amount: 0,
    // Death Benefit Option changes
    change_option_a_to_b: false,
    change_option_b_to_a: false,
    option_change_now: false,
    option_change_age: 0
  });

  // State for the new modify face amount by year functionality
  const [modifyByYearData, setModifyByYearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40, // Will be updated with actual current age
      end: 100
    },
    policyYearRange: {
      start: 1, // Will be updated with actual current policy year
      end: 100
    },
    calendarYearRange: {
      start: 2024, // Will be updated with actual current year
      end: 2100
    },
    isEditing: false,
    tableData: [] as TableRowData[]
  });



  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    faceAmount: number;
  };
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // State for section 2 (Option A to B) year functionality
  const [section2YearData, setSection2YearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40
    },
    policyYearRange: {
      start: 1
    },
    calendarYearRange: {
      start: 2024
    }
  });

  // State for section 3 (Option B to A) year functionality
  const [section3YearData, setSection3YearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40
    },
    policyYearRange: {
      start: 1
    },
    calendarYearRange: {
      start: 2024
    }
  });

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
  };

  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dob = selectedCustomerData.details.DOB;
    console.log('DOB from data:', dob); // Debug log

    // Handle different date formats
    let birthDate: Date;

    if (dob.includes('.')) {
      // Format: DD.MM.YYYY
      const dobParts = dob.split('.');
      if (dobParts.length !== 3) return 40;
      const [day, month, year] = dobParts.map(Number);
      birthDate = new Date(year, month - 1, day);
    } else if (dob.includes('/')) {
      // Format: MM/DD/YYYY or DD/MM/YYYY
      const dobParts = dob.split('/');
      if (dobParts.length !== 3) return 40;
      const [first, second, year] = dobParts.map(Number);
      // Assume MM/DD/YYYY format
      birthDate = new Date(year, first - 1, second);
    } else if (dob.includes('-')) {
      // Format: YYYY-MM-DD
      birthDate = new Date(dob);
    } else {
      return 40; // Default if format not recognized
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('Calculated age:', age); // Debug log
    return Math.max(0, age); // Ensure age is not negative
  };

  // Calculate current policy year from issue date
  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      const issue = new Date(issueDate);
      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      // Calculate total months more accurately
      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1; // Add current month if we've passed the issue day
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1; // Default if no issue date
  };

  // Get current calendar year
  const getCurrentYear = (): number => {
    return new Date().getFullYear();
  };

  // Initialize ranges and face amount data with actual values
  React.useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    // Initialize face amount from policy data
    if (selectedPolicyData?.coverage) {
      const faceAmountFromPolicy = parseInt(selectedPolicyData.coverage.replace(/[^0-9]/g, '') || '0');
      if (faceAmountFromPolicy > 0) {
        setFaceAmountData(prev => ({
          ...prev,
          current_death_benefit: faceAmountFromPolicy
        }));
      }
    }

    setModifyByYearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));

    // Initialize section 2 ranges
    setSection2YearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge
      },
      policyYearRange: {
        start: currentPolicyYear
      },
      calendarYearRange: {
        start: currentYear
      }
    }));

    // Initialize section 3 ranges
    setSection3YearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge
      },
      policyYearRange: {
        start: currentPolicyYear
      },
      calendarYearRange: {
        start: currentYear
      }
    }));

  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data based on selected types and ranges
  const generateTableData = (): TableRowData[] => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = modifyByYearData;

    // Determine which range to use based on selected types
    let startYear = 0;
    let endYear = 0;

    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }

    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];

    // Limit to maximum 12 entries
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        faceAmount: faceAmountData.current_death_benefit // Default face amount
      };

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  React.useEffect(() => {
    const newTableData = generateTableData();
    setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
  }, [modifyByYearData.selectedTypes, modifyByYearData.ageRange, modifyByYearData.policyYearRange, modifyByYearData.calendarYearRange, faceAmountData.current_death_benefit]);

  // Update form data
  const updateFormData = (field: keyof typeof faceAmountData, value: any) => {
    setFaceAmountData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save scenario to database
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      showNotification('Please select a customer and policy first!', 'error');
      return;
    }

    setIsSaving(true);
    try {
      // Get current age and policy year for calculations
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();

      // Get policy ID with better error handling
      const policyId = selectedPolicyData.id || selectedCustomerData.policyNumber || selectedCustomerData.customerId;
      const policyIdNumber = parseInt(policyId?.toString() || '0');

      if (policyIdNumber <= 0) {
        showNotification('Invalid policy ID. Please select a valid policy.', 'error');
        return;
      }

      console.log('🔍 Face Amount Save - Policy ID:', policyIdNumber, 'from:', policyId);

      // Prepare face amount data for API
      // Calculate the option change age from section data
      let calculatedOptionChangeAge = faceAmountData.option_change_age;

      // If option A to B is selected and not changing now, get age from section2YearData
      if (faceAmountData.change_option_a_to_b && !faceAmountData.option_change_now && section2YearData.selectedTypes.age) {
        calculatedOptionChangeAge = section2YearData.ageRange.start;
      }

      // If option B to A is selected and not changing now, get age from section3YearData
      if (faceAmountData.change_option_b_to_a && !faceAmountData.option_change_now && section3YearData.selectedTypes.age) {
        calculatedOptionChangeAge = section3YearData.ageRange.start;
      }

      const faceAmountApiData: FaceAmountIllustrationData = {
        policy_id: policyIdNumber,
        current_death_benefit: faceAmountData.current_death_benefit,
        current_death_benefit_option: faceAmountData.current_death_benefit_option,
        want_to_change: faceAmountData.want_to_change,
        change_immediately: faceAmountData.change_immediately,
        change_amount: faceAmountData.change_amount,
        change_option_a_to_b: faceAmountData.change_option_a_to_b,
        change_option_b_to_a: faceAmountData.change_option_b_to_a,
        option_change_now: faceAmountData.option_change_now,
        option_change_age: calculatedOptionChangeAge,
        schedule_data: modifyByYearData.tableData.length > 0 ?
          modifyByYearData.tableData.map(row => ({
            age: row.age,
            policy_year: parseInt(row.policyYear.replace('Year ', '')),
            calendar_year: row.calendarYear,
            face_amount: row.faceAmount
          })) : undefined,
        section2_data: faceAmountData.change_option_a_to_b && !faceAmountData.option_change_now &&
          (section2YearData.selectedTypes.age || section2YearData.selectedTypes.policyYear || section2YearData.selectedTypes.calendarYear) ? {
          selected_type: section2YearData.selectedTypes.age ? 'age' as const :
                        section2YearData.selectedTypes.policyYear ? 'policyYear' as const :
                        'calendarYear' as const,
          start_value: section2YearData.selectedTypes.age ? section2YearData.ageRange.start :
                      section2YearData.selectedTypes.policyYear ? section2YearData.policyYearRange.start :
                      section2YearData.calendarYearRange.start
        } : undefined,
        section3_data: faceAmountData.change_option_b_to_a && !faceAmountData.option_change_now &&
          (section3YearData.selectedTypes.age || section3YearData.selectedTypes.policyYear || section3YearData.selectedTypes.calendarYear) ? {
          selected_type: section3YearData.selectedTypes.age ? 'age' as const :
                        section3YearData.selectedTypes.policyYear ? 'policyYear' as const :
                        'calendarYear' as const,
          start_value: section3YearData.selectedTypes.age ? section3YearData.ageRange.start :
                      section3YearData.selectedTypes.policyYear ? section3YearData.policyYearRange.start :
                      section3YearData.calendarYearRange.start
        } : undefined
      };

      // Debug: Log the data being sent
      console.log('🔍 Face Amount API Data being sent:', faceAmountApiData);

      // Validate data before saving
      console.log('🔍 Face Amount - Validating data before save...');
      const validationErrors = validateFaceAmountData(faceAmountApiData);
      if (validationErrors.length > 0) {
        console.warn('❌ Face Amount validation failed:', validationErrors);
        showNotification(`Validation errors: ${validationErrors.join(', ')}`, 'error');
        return;
      }

      console.log('✅ Face Amount validation passed, proceeding with save...');

      // Save to database via API
      const result = await saveFaceAmountIllustration(faceAmountApiData, currentAge, currentPolicyYear);

      console.log('📊 Face Amount save result:', result);

      if (result.status === 'SUCCESS') {
        showNotification('Face Amount illustration saved successfully to database!');
        console.log('✅ Face Amount scenario saved successfully');

        // ✅ Add Face Amount scenario to current session for immediate display
        try {
          // 🔥 CAPTURE SELECTED OPTIONS BEFORE RESETTING FORM
          const getSelectedOptionDescription = () => {
            const selectedOptions = [];

            // Check for face amount changes
            if (faceAmountData.want_to_change && faceAmountData.change_immediately) {
              selectedOptions.push(`Change face amount to $${faceAmountData.change_amount?.toLocaleString() || '0'}`);
            }

            // Check for option changes
            if (faceAmountData.change_option_a_to_b) {
              if (faceAmountData.option_change_now) {
                selectedOptions.push('Change from Option A to Option B now');
              } else {
                selectedOptions.push(`Change from Option A to Option B at age ${faceAmountData.option_change_age || 'TBD'}`);
              }
            }

            if (faceAmountData.change_option_b_to_a) {
              if (faceAmountData.option_change_now) {
                selectedOptions.push('Change from Option B to Option A now');
              } else {
                selectedOptions.push(`Change from Option B to Option A at age ${faceAmountData.option_change_age || 'TBD'}`);
              }
            }

            // Check for modify by year selections
            if (modifyByYearData.selectedTypes.age) {
              selectedOptions.push(`Modify by age starting from ${modifyByYearData.ageRange.start}`);
            }
            if (modifyByYearData.selectedTypes.policyYear) {
              selectedOptions.push(`Modify by policy year starting from ${modifyByYearData.policyYearRange.start}`);
            }
            if (modifyByYearData.selectedTypes.calendarYear) {
              selectedOptions.push(`Modify by calendar year starting from ${modifyByYearData.calendarYearRange.start}`);
            }

            // If no specific changes selected, return default
            if (selectedOptions.length === 0) {
              return 'No changes to face amount';
            }

            return selectedOptions.join(', ');
          };

          // 🔥 CAPTURE THE DESCRIPTION BEFORE FORM RESET
          const selectedOptionDescription = getSelectedOptionDescription();
          console.log('🔍 Captured selected options before reset:', selectedOptionDescription);

          const scenarioData = {
            id: `face-amount-${Date.now()}`,
            name: 'FACE AMOUNT',
            policyId: selectedPolicyData?.id?.toString() || '',
            asIsDetails: 'Face Amount Analysis',
            category: 'face-amount' as const,
            keyPoints: [
              `Type: Face Amount`,
              `Question: Face Amount Changes`,
              `Selected Option: ${selectedOptionDescription}`,
            ],
            whatIfOptions: [selectedOptionDescription],
            data: { ...faceAmountData }, // Create a copy of the current data
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await addScenario(scenarioData);
          console.log('✅ Face Amount scenario added to current session with options:', selectedOptionDescription);
        } catch (error) {
          console.error('❌ Error adding Face Amount scenario to session:', error);
        }

        // Reset form after successful save
        setFaceAmountData({
          current_death_benefit: parseInt(selectedPolicyData.coverage?.replace(/[^0-9]/g, '') || '500000'),
          current_death_benefit_option: 'A',
          want_to_change: false,
          change_immediately: false,
          change_amount: 0,
          change_option_a_to_b: false,
          change_option_b_to_a: false,
          option_change_now: false,
          option_change_age: 0
        });

        // Reset modify by year data
        setModifyByYearData(prev => ({
          ...prev,
          selectedTypes: { age: false, policyYear: false, calendarYear: false },
          isEditing: false,
          tableData: []
        }));
      } else {
        console.error('❌ Face Amount save failed:', result.message);
        showNotification(`Failed to save Face Amount illustration: ${result.message}`, 'error');
      }
    } catch (error) {
      console.error('❌ Error saving Face Amount illustration:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showNotification(`Error saving Face Amount illustration: ${errorMessage}`, 'error');
    } finally {
      setIsSaving(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="space-y-6">
        {/* <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FACE AMOUNT ILLUSTRATION</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure the face amount and death benefit options for the selected policy.</p>
        </div> */}

        {/* Notification */}
        {notification && (
          <Notification
            message={notification.message}
            type={notification.type}
            onClose={() => setNotification(null)}
          />
        )}

        {/* Show message if no policy is selected */}
        {(!selectedCustomerData || !selectedPolicyData) ? (
          <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
                <p className="text-yellow-700 dark:text-yellow-300">
                  Please go to the Policy Selection tab first to search and select a customer policy before configuring the Face Amount illustration.
                </p>
                <Button
                  onClick={() => setActiveTab('policy-selection')}
                  variant="outline"
                  className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <>
            {/* Section 1: Current Face Amount Death Benefit */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              Scenarios
            </h3>

            {/* Scenarios Description - moved to top */}
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 mb-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                You may want to increase or decrease your policy's face amount based on your current protection needs. Changing the death benefit will impact premiums, cash value growth, and overall policy performance. Scenarios will be based on the Current Interest Rate.
              </p>
            </div>

            {/* Current Face Amount Death Benefit Question */}
            <div className="mb-6">
              <h4 className="text-xl font-bold text-black mb-4">Current Face Amount Death Benefit</h4>

              {/* Face Amount Display Box */}
              <div className="bg-green-50 p-6 rounded-lg border border-green-200 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-lg font-semibold text-black">Death Benefit Amount</p>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-green-700">
                      ${(selectedPolicyData?.coverage?.replace(/[^0-9]/g, '') || faceAmountData.current_death_benefit.toString()).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </div>
                    <p className="text-sm text-gray-500">Death Benefit</p>
                  </div>
                </div>
              </div>

              {/* Options under the face amount */}
              <div className="space-y-4">
                <div>
                  <label className="flex items-center text-lg font-semibold text-black mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_immediately}
                      onChange={(e) => updateFormData('change_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to New Face Amount.
                  </label>
                  {faceAmountData.change_immediately && (
                    <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="flex items-center text-lg font-semibold text-black mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.want_to_change}
                      onChange={(e) => updateFormData('want_to_change', e.target.checked)}
                      className="mr-2"
                    />
                    Modify face amount by year
                  </label>
                  {faceAmountData.want_to_change && (
                    <div className="mt-4 space-y-6">
                      {/* Single Container for Type Selection and Range Controls */}
                      <div className="bg-white p-6 rounded-lg border border-gray-300">
                        {/* Type Selection Checkboxes - only one can be selected */}
                        <div className="grid grid-cols-3 gap-4 mb-6">
                          <label className="flex items-center text-black font-semibold">
                            <input
                              type="checkbox"
                              checked={modifyByYearData.selectedTypes.age}
                              onChange={(e) => setModifyByYearData(prev => ({
                                ...prev,
                                selectedTypes: {
                                  age: e.target.checked,
                                  policyYear: false,
                                  calendarYear: false
                                }
                              }))}
                              className="mr-2"
                            />
                            Age
                          </label>
                          <label className="flex items-center text-black font-semibold">
                            <input
                              type="checkbox"
                              checked={modifyByYearData.selectedTypes.policyYear}
                              onChange={(e) => setModifyByYearData(prev => ({
                                ...prev,
                                selectedTypes: {
                                  age: false,
                                  policyYear: e.target.checked,
                                  calendarYear: false
                                }
                              }))}
                              className="mr-2"
                            />
                            Policy Year
                          </label>
                          <label className="flex items-center text-black font-semibold">
                            <input
                              type="checkbox"
                              checked={modifyByYearData.selectedTypes.calendarYear}
                              onChange={(e) => setModifyByYearData(prev => ({
                                ...prev,
                                selectedTypes: {
                                  age: false,
                                  policyYear: false,
                                  calendarYear: e.target.checked
                                }
                              }))}
                              className="mr-2"
                            />
                            Calendar Year
                          </label>
                        </div>

                      {/* Age Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.age && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Age Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.ageRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.ageRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.policyYear && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Policy Year Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.policyYearRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.policyYearRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars */}
                      {modifyByYearData.selectedTypes.calendarYear && (
                        <div className="space-y-4">
                          {/* <h4 className="text-lg font-semibold text-black">Calendar Year Range</h4> */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.calendarYearRange.start}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {modifyByYearData.calendarYearRange.end}
                                </span>
                                <button
                                  onClick={() => setModifyByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      </div>

                      {/* Buttons Row - moved outside container and above table */}
                      <div className="flex justify-between items-center mt-6 mb-4">
                        <button
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
                        >
                          View Year by Year Details
                        </button>
                        <button
                          onClick={() => setModifyByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                        >
                          {modifyByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                        </button>
                      </div>

                      {/* Data Table */}
                      <div className="mt-4">
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Face Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {modifyByYearData.tableData.length === 0 ? (
                                <tr>
                                  <td
                                    colSpan={4}
                                    className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                  >
                                    Select year range to populate table
                                  </td>
                                </tr>
                              ) : (
                                modifyByYearData.tableData.map((row, index) => (
                                  <tr key={index}>
                                    <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.age}</td>
                                    <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.policyYear}</td>
                                    <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.calendarYear}</td>
                                    <td className="border border-gray-300 px-4 py-2">
                                      <input
                                        type="number"
                                        value={row.faceAmount}
                                        readOnly={!modifyByYearData.isEditing}
                                        onChange={(e) => {
                                          if (modifyByYearData.isEditing) {
                                            const newTableData = [...modifyByYearData.tableData];
                                            newTableData[index].faceAmount = parseInt(e.target.value) || 0;
                                            setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
                                          }
                                        }}
                                        className={`w-full p-2 border rounded text-black font-semibold ${
                                          modifyByYearData.isEditing
                                            ? 'border-blue-300 bg-white'
                                            : 'border-gray-300 bg-gray-100'
                                        }`}
                                        step="10000"
                                      />
                                    </td>
                                  </tr>
                                ))
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Section 2: Death Benefit Option A to B */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">2. Your current Death Benefit Option is A (Level). Do you want to switch to option B (level to increasing)?</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <div className="space-y-4">
                  {/* Option: Change to Option B now */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_option_a_to_b && faceAmountData.option_change_now}
                        onChange={(e) => {
                          if (e.target.checked) {
                            // Select this option
                            updateFormData('change_option_a_to_b', true);
                            updateFormData('option_change_now', true);
                          } else {
                            // Unselect
                            updateFormData('change_option_a_to_b', false);
                            updateFormData('option_change_now', false);
                          }
                        }}
                        className="w-5 h-5 text-black"
                      />
                      <span>Change to Option B (Level to Increasing) now</span>
                    </label>
                  </div>

                  {/* Option: Change starting from age */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_option_a_to_b && !faceAmountData.option_change_now}
                        onChange={(e) => {
                          if (e.target.checked) {
                            // Select this option
                            updateFormData('change_option_a_to_b', true);
                            updateFormData('option_change_now', false);
                          } else {
                            // Unselect
                            updateFormData('change_option_a_to_b', false);
                            updateFormData('option_change_now', false);
                          }
                        }}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>Modify to option B later</span>
                        {/* Age, Policy Year, Calendar Year Controls for Section 2 */}
                        {faceAmountData.change_option_a_to_b && !faceAmountData.option_change_now && (
                          <div className="mt-6 bg-white p-6 rounded-lg border border-gray-300">
                            {/* Type Selection Checkboxes - only one can be selected */}
                            <div className="grid grid-cols-3 gap-4 mb-6">
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section2YearData.selectedTypes.age}
                                  onChange={(e) => setSection2YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: e.target.checked,
                                      policyYear: false,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Age
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section2YearData.selectedTypes.policyYear}
                                  onChange={(e) => setSection2YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: e.target.checked,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Policy Year
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section2YearData.selectedTypes.calendarYear}
                                  onChange={(e) => setSection2YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: false,
                                      calendarYear: e.target.checked
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Calendar Year
                              </label>
                            </div>

                            {/* Age Range Toggle Bar */}
                            {section2YearData.selectedTypes.age && (
                              <div className="space-y-4 mb-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        ageRange: { start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section2YearData.ageRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        ageRange: { start: Math.min(100, prev.ageRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Policy Year Range Toggle Bar */}
                            {section2YearData.selectedTypes.policyYear && (
                              <div className="space-y-4 mb-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        policyYearRange: { start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section2YearData.policyYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        policyYearRange: { start: Math.min(100, prev.policyYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Calendar Year Range Toggle Bar */}
                            {section2YearData.selectedTypes.calendarYear && (
                              <div className="space-y-4">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        calendarYearRange: { start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section2YearData.calendarYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection2YearData(prev => ({
                                        ...prev,
                                        calendarYearRange: { start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </label>
                  </div>
                </div>



              </div>
            </div>
          </div>

          {/* Section 3: Death Benefit Option B to A */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">3. Your current Death Benefit Option is B (Increasing). Switch to option A (Increasing to Level)?</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <div className="space-y-4">
                  {/* Option: Change to Option A now */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_option_b_to_a && faceAmountData.option_change_now}
                        onChange={(e) => {
                          if (e.target.checked) {
                            // Select this option
                            updateFormData('change_option_b_to_a', true);
                            updateFormData('option_change_now', true);
                          } else {
                            // Unselect
                            updateFormData('change_option_b_to_a', false);
                            updateFormData('option_change_now', false);
                          }
                        }}
                        className="w-5 h-5 text-black"
                      />
                      <span>Change to Option A (Increasing to Level) now</span>
                    </label>
                  </div>

                  {/* Option: Change starting from age */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_option_b_to_a && !faceAmountData.option_change_now}
                        onChange={(e) => {
                          if (e.target.checked) {
                            // Select this option
                            updateFormData('change_option_b_to_a', true);
                            updateFormData('option_change_now', false);
                          } else {
                            // Unselect
                            updateFormData('change_option_b_to_a', false);
                            updateFormData('option_change_now', false);
                          }
                        }}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>Modify the option A later</span>
                        {/* Age, Policy Year, Calendar Year Controls for Section 3 */}
                        {faceAmountData.change_option_b_to_a && !faceAmountData.option_change_now && (
                          <div className="mt-6 bg-white p-6 rounded-lg border border-gray-300">
                            {/* Type Selection Checkboxes - only one can be selected */}
                            <div className="grid grid-cols-3 gap-4 mb-6">
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section3YearData.selectedTypes.age}
                                  onChange={(e) => setSection3YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: e.target.checked,
                                      policyYear: false,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Age
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section3YearData.selectedTypes.policyYear}
                                  onChange={(e) => setSection3YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: e.target.checked,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Policy Year
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={section3YearData.selectedTypes.calendarYear}
                                  onChange={(e) => setSection3YearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: false,
                                      calendarYear: e.target.checked
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Calendar Year
                              </label>
                            </div>

                            {/* Age Range Toggle Bar */}
                            {section3YearData.selectedTypes.age && (
                              <div className="space-y-4 mb-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        ageRange: { start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section3YearData.ageRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        ageRange: { start: Math.min(100, prev.ageRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Policy Year Range Toggle Bar */}
                            {section3YearData.selectedTypes.policyYear && (
                              <div className="space-y-4 mb-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        policyYearRange: { start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section3YearData.policyYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        policyYearRange: { start: Math.min(100, prev.policyYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Calendar Year Range Toggle Bar */}
                            {section3YearData.selectedTypes.calendarYear && (
                              <div className="space-y-4">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3 w-48">
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        calendarYearRange: { start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {section3YearData.calendarYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setSection3YearData(prev => ({
                                        ...prev,
                                        calendarYearRange: { start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </label>
                  </div>
                </div>



              </div>
            </div>
          </div>

          {/* Debug Information (Development Only) */}
          {import.meta.env.DEV && (
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">🐛 Face Amount Debug Info</h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Policy ID:</strong> {selectedPolicyData?.id || 'Not set'}
                  </div>
                  <div>
                    <strong>Customer ID:</strong> {selectedCustomerData?.customerId || 'Not set'}
                  </div>
                  <div>
                    <strong>Want to Change:</strong> {faceAmountData.want_to_change ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Change Immediately:</strong> {faceAmountData.change_immediately ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Change Amount:</strong> ${faceAmountData.change_amount?.toLocaleString() || '0'}
                  </div>
                  <div>
                    <strong>Current Death Benefit:</strong> ${faceAmountData.current_death_benefit?.toLocaleString() || '0'}
                  </div>
                  <div>
                    <strong>Option A to B:</strong> {faceAmountData.change_option_a_to_b ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Option B to A:</strong> {faceAmountData.change_option_b_to_a ? '✅ Yes' : '❌ No'}
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                  <strong>Validation Status:</strong> {
                    (() => {
                      const hasPolicy = !!selectedPolicyData;
                      const hasValidBenefit = faceAmountData.current_death_benefit > 0;
                      const hasValidChange = !faceAmountData.want_to_change ||
                        (faceAmountData.change_immediately && faceAmountData.change_amount > 0) ||
                        (!faceAmountData.change_immediately);

                      if (hasPolicy && hasValidBenefit && hasValidChange) {
                        return '✅ Ready to Save';
                      } else {
                        const issues = [];
                        if (!hasPolicy) issues.push('No policy selected');
                        if (!hasValidBenefit) issues.push('Invalid death benefit');
                        if (!hasValidChange) issues.push('Invalid change configuration');
                        return `❌ Issues: ${issues.join(', ')}`;
                      }
                    })()
                  }
                </div>
              </div>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Face Amount Illustration</span>
            </Button>
            <Button
              onClick={() => {
                // Reset form to initial state
                setFaceAmountData({
                  current_death_benefit: parseInt(selectedPolicyData?.coverage?.replace(/[^0-9]/g, '') || '0'),
                  current_death_benefit_option: 'A',
                  want_to_change: false,
                  change_immediately: false,
                  change_amount: 0,
                  change_option_a_to_b: false,
                  change_option_b_to_a: false,
                  option_change_now: false,
                  option_change_age: 0
                });
                // Reset modify by year data
                setModifyByYearData(prev => ({
                  ...prev,
                  selectedTypes: { age: false, policyYear: false, calendarYear: false },
                  isEditing: false,
                  tableData: []
                }));
                showNotification('Form reset to initial state!');
              }}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Form</span>
            </Button>
          </div>



          {/* Selected Scenarios Section */}
          </>
        )}
      </div>
    </div>
  );
};

export default FaceAmountPage; 