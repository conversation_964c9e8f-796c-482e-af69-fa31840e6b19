import React, { useState, useEffect } from 'react';
import { <PERSON>R<PERSON>, Calculator, Search, User, Shield, DollarSign, FileText, CreditCard, Activity, Loader2 } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

/**
 * PolicySelection Component - Backend Integration Ready
 *
 * This component connects to the backend API endpoints:
 * 1. POST /policy/policy/api/policy/search/ui - Search for customer policies
 * 2. GET /policy/policy/insurer/view - Get complete customer information for selected policy
 */

// TypeScript interfaces
interface Policy {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
  status: string;
  issueDate?: string;
  nextDueDate?: string;
  cashValue?: string;
  paymentFrequency?: string;
}

interface CustomerDetails {
  DOB: string;
  Email: string;
  Phone: string;
  Address: string;
  Occupation: string;
  "Annual Income": string;
  "Customer ID": string;
  "Policy Number": string;
  "Policy Type": string;
  Status: string;
  "Available Policies": Policy[];
}

interface CurrentCustomerData {
  name: string;
  policyNumber: string;
  customerId: string;
  details: CustomerDetails;
}

interface PaymentHistory {
  date: string;
  amount: string;
  status: string;
}

interface TransactionHistory {
  id: string;
  date: string;
  type: string;
  amount: string;
  remarks: string;
}

interface Rider {
  name: string;
  coverage: string;
  status: string;
}

interface PolicyDetailsResponse {
  status: string;
  message: string;
  data: {
    // Policy Information
    POLICY_ID: number;
    CUSTOMER_ID: number;
    POLICY_TYPE: string;
    POLICY_STATUS: string;
    POLICY_ISSUED_DATE: string;
    POLICY_EXPIRY_DATE: string;
    PREMIUM_AMOUNT: number;
    FACE_AMOUNT: number;
    POLICY_TERM_YEARS: number;
    CURRENT_CASH_VALUE: number;

    // Customer Information
    CUSTOMER_FIRST_NAME: string;
    CUSTOMER_MIDDLE_NAME: string;
    CUSTOMER_LAST_NAME: string;
    SALUTATION: string;
    GENDER: string;
    DATE_OF_BIRTH: string;
    CONTACT_NUMBER: string;
    EMAIL: string;
    CUSTOMER_ADDRESS_LINE_1: string;
    CUSTOMER_ADDRESS_LINE_2: string;
    CUSTOMER_CITY: string;
    CUSTOMER_STATE: string;
    CUSTOMER_ZIP: string;
    CUSTOMER_COUNTRY: string;

    // Financial Information
    LOAN_AMOUNT_DISBURESED: number;
    MINIMUM_INTEREST_RATE_IN_PERCENTAGE: number;
    GUARANTEED_INTEREST_RATE_IN_PERCENTAGE: number;
    CURRENT_INTEREST_RATE_IN_PERCENTAGE: number;
    WITHDRAWL_AMOUNT: number;
    RIDER_APPLICABLE: number;

    // Agent Information
    AGENT_CODE: string;
    AGENT_SALUTATION: string;
    AGENT_FIRST_NAME: string;
    AGENT_MIDDLE_NAME: string;
    AGENT_LAST_NAME: string;
    AGENT_NAME: string;
    AGENT_GENDER: string;
    AGENT_EMAIL: string;
    AGENT_PHONE_NUMBER: string;
    AGENT_STATE: string;
    AGENT_STATUS: string;

    // Product Information
    INSURANCE_PRODUCT_ID: number;
    INSURANCE_PRODUCT_CODE: string;
    INSURANCE_PRODUCT_NAME: string;
    INSURANCE_PRODUCT_LINE_OF_BUSINESS: string;
    INSURANCE_PRODUCT_DESCRIPTION: string;
    INSURANCE_PRODUCT_STATUS: string;
    INSURANCE_PRODUCT_EXPIRY_DATE: string;

    // Company Information
    INSURANCE_COMPANY_NAME: string;
    COMPANY_PHONE: string;
    COMPANY_EMAIL: string;
    COMPANY_ADDRESS_LINE_1: string;
    COMPANY_ADDRESS_LINE_2: string;
    COMPANY_CITY: string;
    COMPANY_STATE: string;
    COMPANY_ZIP: string;
    COMPANY_COUNTRY: string;

    // Related Data Arrays
    riders: Array<{
      RIDER_ID: number;
      POLICY_ID: number;
      RIDER_NAME: string;
      RIDER_ISSUE_DATE: string;
      RIDER_COVERAGE_AMOUNT: number;
      RIDER_PREMIUM_AMOUNT: number;
      RIDER_STATUS: string;
    }>;

    beneficiaries: Array<{
      BENEFICIARY_ID: number;
      POLICY_ID: number;
      BENEFICIARY_FIRST_NAME: string;
      BENEFICIARY_MIDDLE_NAME: string;
      BENEFICIARY_LAST_NAME: string;
      SALUTATION: string;
      BENEFICIARY_GENDER: string;
      BENEFICIARY_RELATIONSHIP: string;
    }>;

    transactions: Array<{
      TRANSACTION_ID: number;
      POLICY_ID: number;
      TRANSACTION_TYPE: string;
      TRANSACTION_DATE: string;
      TRANSACTION_STATUS: string;
      TRANSACTION_AMOUNT: string;
      TRANSACTION_REQUEST_BY: number;
    }>;

    loans: Array<{
      LOAN_ID: number;
      POLICY_ID: number;
      LOAN_AMOUNT: number;
      LOAN_ISSUED_DATE: string;
      LOAN_PERIOD_IN_MONTHS: number;
      LOAN_INTEREST_RATE_IN_PERCENTAGE: number;
      LOAN_STATUS: string;
      repayments: Array<{
        SCHEDULE_ID: number;
        LOAN_ID: number;
        SCHEDULE_TYPE: string;
        LOAN_SCHEDULED_DUE_DATE: string;
        LOAN_ACTUAL_REPAYMENT_DATE: string;
        LOAN_REPAYMENT_AMOUNT: number;
        STATUS: string;
      }>;
    }>;

    [key: string]: any; // For any additional fields
  };
}

const PolicySelection = () => {
  const { policySearchFormData, setPolicySearchFormData } = useDashboard();

  // Date formatting function to convert dates to MM-DD-YYYY format
  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return 'N/A';

    try {
      // Handle different date formats that might come from backend
      let date: Date;

      // If it's already in MM-DD-YYYY format, parse it correctly
      if (dateString.includes('-') && dateString.split('-')[0].length <= 2) {
        const [month, day, year] = dateString.split('-');
        date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      // If it's in YYYY-MM-DD format
      else if (dateString.includes('-') && dateString.split('-')[0].length === 4) {
        date = new Date(dateString);
      }
      // If it's in other formats, try to parse directly
      else {
        date = new Date(dateString);
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return dateString; // Return original if can't parse
      }

      // Format to MM-DD-YYYY
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${month}-${day}-${year}`;
    } catch (error) {
      console.warn('Error formatting date:', dateString, error);
      return dateString || 'N/A'; // Return original or N/A if error
    }
  };

  // Initialize form data from context or use empty values
  const [customerName, setCustomerName] = useState(policySearchFormData?.customerName || '');
  const [policyNumber, setPolicyNumber] = useState(policySearchFormData?.policyNumber || '');
  const [customerId, setCustomerId] = useState(policySearchFormData?.customerId || '');

  // Update context whenever form data changes
  const updateFormData = (field: string, value: string) => {
    const currentData = {
      customerName,
      policyNumber,
      customerId
    };

    const updatedData = { ...currentData, [field]: value };
    setPolicySearchFormData(updatedData);

    // Update local state
    switch (field) {
      case 'customerName':
        setCustomerName(value);
        break;
      case 'policyNumber':
        setPolicyNumber(value);
        break;
      case 'customerId':
        setCustomerId(value);
        break;
    }
  };

  // Reset form data
  const resetFormData = () => {
    setCustomerName('');
    setPolicyNumber('');
    setCustomerId('');
    setPolicySearchFormData(null);
    setCurrentCustomerData(null);
    setSelectedPolicy(null);
    setSearchClicked(false);
    setError(null);
  };
  const [searchClicked, setSearchClicked] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [currentCustomerData, setCurrentCustomerData] = useState<CurrentCustomerData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [policyDetails, setPolicyDetails] = useState<PolicyDetailsResponse | null>(null);
  const { setActiveTab, setSelectedCustomerData, setSelectedPolicyData, fetchAllowedIllustrationTypes, selectedCustomerData, selectedPolicyData } = useDashboard();

  // Sync form data when context changes (e.g., when returning from another tab)
  useEffect(() => {
    if (policySearchFormData) {
      setCustomerName(policySearchFormData.customerName);
      setPolicyNumber(policySearchFormData.policyNumber);
      setCustomerId(policySearchFormData.customerId);
    }
  }, [policySearchFormData]);

  // Restore complete policy state when component mounts (for page refresh or tab navigation)
  useEffect(() => {
    if (selectedCustomerData && selectedPolicyData) {
      // Restore customer data
      setCurrentCustomerData(selectedCustomerData);
      setSearchClicked(true);

      // Restore selected policy
      const restoredPolicy: Policy = {
        id: selectedPolicyData.policyId?.toString() || selectedCustomerData.details["Policy Number"] || '',
        name: selectedPolicyData.policyType || selectedCustomerData.details["Policy Type"] || 'Unknown Policy',
        description: `Policy for ${selectedCustomerData.name}`,
        coverage: selectedPolicyData.faceAmount?.toString() || 'N/A',
        premium: selectedPolicyData.premiumAmount ? `$${selectedPolicyData.premiumAmount.toLocaleString()} annually` : "N/A",
        features: [],
        status: selectedPolicyData.policyStatus || selectedCustomerData.details.Status || 'Unknown',
        issueDate: selectedPolicyData.policyIssuedDate || 'N/A',
        nextDueDate: 'N/A',
        cashValue: selectedPolicyData.currentCashValue?.toString() || 'N/A',
        paymentFrequency: "Annually"
      };

      setSelectedPolicy(restoredPolicy);
      setPolicyDetails(selectedPolicyData as any); // Cast to match expected type

      console.log('🔄 Policy state restored from context:', {
        customerName: selectedCustomerData.name,
        policyId: restoredPolicy.id,
        policyType: restoredPolicy.name
      });
    }
  }, [selectedCustomerData, selectedPolicyData]);

  // API Base URL - loaded from .env file
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
  if (!API_BASE_URL) {
    throw new Error('VITE_API_BASE_URL environment variable is not set in .env file');
  }

  // 🔥 BACKEND ENDPOINT 1: SEARCH API
  const searchCustomerPolicies = async (customerId: string, policyNumber: string, customerName: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 🚀 BACKEND REQUEST: POST request with JSON body
      // Extract first name only since backend searches by first name
      const firstName = customerName.trim().split(' ')[0];
      const requestBody = {
        customer_id: parseInt(customerId.trim()) || null,
        policy_id: parseInt(policyNumber.trim()) || null,
        customer_name: firstName || null,
      };

      const response = await fetch(`${API_BASE_URL}/api/policy/search/ui`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      // 📊 BACKEND RESPONSE: Customer data with available policies
      const data = await response.json();
      
      if (!response.ok) {
        // Handle backend error responses
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error('Error searching customer policies:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 BACKEND ENDPOINT 2: VIEW API
  const getCompleteCustomerInfo = async (policyId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 🚀 BACKEND REQUEST: POST request with query parameter
      const response = await fetch(`${API_BASE_URL}/View_Policy/ui/viewed_policy?policy_id=${policyId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 📊 BACKEND RESPONSE: Complete customer information
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching complete customer information:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 BACKEND INTEGRATION POINT 1: SEARCH FUNCTIONALITY
  const handleSearch = async () => {
    if (!customerId.trim() && !policyNumber.trim() && !customerName.trim()) {
      setError('Please enter at least one search field');
      return;
    }

    setSearchClicked(true);
    setError(null);
    setCurrentCustomerData(null);
    setSelectedPolicy(null);
    setPolicyDetails(null);

    try {
      // 🚀 BACKEND CALL: POST /policy/policy/api/policy/search/ui
      const customerData = await searchCustomerPolicies(customerId, policyNumber, customerName);

      if (customerData && customerData.POLICY_DETAILS && customerData.POLICY_DETAILS.length > 0) {
        // ✅ SUCCESS: Transform backend data to frontend format
        const policies = customerData.POLICY_DETAILS.map((policy: any) => ({
          id: policy.policyId.toString(),
          name: policy.policyType,
          description: `Policy for ${policy.customer_name}`,
          coverage: policy.faceAmount.toString(),
          premium: policy.premiumAmount ? `$${policy.premiumAmount.toLocaleString()} annually` : "N/A", // ✅ Now using actual premium from backend
          features: [],
          status: policy.status,
          issueDate: formatDate(new Date().toISOString().split('T')[0]), // Placeholder
          nextDueDate: formatDate(policy.maturityDate),
          cashValue: "N/A",
          paymentFrequency: "Annually"
        }));

        // Use actual data from backend response instead of search form inputs
        const firstPolicy = customerData.POLICY_DETAILS[0];
        setCurrentCustomerData({
          name: firstPolicy.customer_name, // ✅ Real customer name from backend
          policyNumber: firstPolicy.policyId.toString(), // ✅ Real policy ID from backend
          customerId: firstPolicy.customerId.toString(), // ✅ Real customer ID from backend
          details: {
            DOB: "N/A", // Backend doesn't provide this yet
            Email: "N/A",
            Phone: "N/A",
            Address: "N/A",
            Occupation: "N/A",
            "Annual Income": "N/A",
            "Customer ID": firstPolicy.customerId.toString(),
            "Policy Number": firstPolicy.policyId.toString(),
            "Policy Type": policies[0]?.name || "N/A",
            Status: policies[0]?.status || "N/A",
            "Available Policies": policies
          }
        });

        // Don't automatically load details - let user select policy first
        // Only auto-load if there's exactly one policy
        if (policies.length === 1) {
          const singlePolicy = policies[0];
          setSelectedPolicy(singlePolicy);

          // 🚀 BACKEND CALL: GET /policy/policy/insurer/view
          const completeCustomerData = await getCompleteCustomerInfo(singlePolicy.id);

          if (completeCustomerData) {
            // ✅ SUCCESS: Set the backend data directly
            setPolicyDetails(completeCustomerData);
          }
        }
      } else {
        // ❌ NO RESULTS: Show error message
        setCurrentCustomerData(null);
        setError('Customer not found. Please check your details and try again.');
      }
    } catch (error: any) {
      // ❌ ERROR: Network or server error
      console.error('Search error:', error);
      setCurrentCustomerData(null);
      setError(error.message || 'Failed to search customer data. Please check your connection and try again.');
    }
  };

  // 🔥 BACKEND INTEGRATION POINT 2: VIEW POLICY DETAILS
  const handleSelectPolicy = async (policy: Policy) => {
    if (!currentCustomerData) return;

    try {
      setSelectedPolicy(policy);
      setPolicyDetails(null);

      // 🚀 BACKEND CALL: GET /policy/policy/insurer/view
      const completeCustomerData = await getCompleteCustomerInfo(policy.id);

      if (completeCustomerData) {
        // ✅ SUCCESS: Set the backend data directly
        setPolicyDetails(completeCustomerData);
      } else {
        // ❌ ERROR: Failed to load policy details
        setError('Failed to load complete customer information');
        setPolicyDetails(null);
      }
    } catch (error: any) {
      // ❌ ERROR: Network or server error
      console.error('Error selecting policy:', error);
      setError(error.message || 'Failed to load complete customer information. Please try again.');
      setPolicyDetails(null);
    }
  };

  // Check if at least one field has content
  const hasAnyInput = customerId.trim() || policyNumber.trim() || customerName.trim();

  const renderSearchSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 className="text-xl font-bold text-black mb-6">
        Search Your Policy
      </h2>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer ID</label>
          <input
            type="text"
            value={customerId}
            onChange={(e) => {
              updateFormData('customerId', e.target.value);
              setError(null);
            }}
            placeholder="Enter customer ID"
            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Policy Number</label>
          <input
            type="text"
            value={policyNumber}
            onChange={(e) => {
              updateFormData('policyNumber', e.target.value);
              setError(null);
            }}
            placeholder="Enter policy number"
            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
          <input
            type="text"
            value={customerName}
            onChange={(e) => {
              updateFormData('customerName', e.target.value);
              setError(null);
            }}
            placeholder="Enter first name only"
            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">Enter first name only</p>
        </div>
      </div>

      {/* Search Button - Only show when there's input */}
      {hasAnyInput && (
        <div className="text-center">
          <button
            onClick={handleSearch}
            disabled={isLoading}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed mx-auto"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <Search className="w-4 h-4" />
                Search
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Search Section */}
      {renderSearchSection()}
      
      {/* Policy Selection - Show when multiple policies found */}
      {searchClicked && currentCustomerData && currentCustomerData.details["Available Policies"].length > 1 && !selectedPolicy && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Multiple Policies Found for {currentCustomerData.name}
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Please select a policy to view complete details:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentCustomerData.details["Available Policies"].map((policy, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                   onClick={() => handleSelectPolicy(policy)}>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{policy.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Policy ID: {policy.id}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Status: <span className="font-medium">{policy.status}</span>
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Coverage: <span className="font-medium">${policy.coverage?.toLocaleString()}</span>
                    </p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectPolicy(policy);
                    }}
                    disabled={isLoading}
                    className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400"
                  >
                    {isLoading && selectedPolicy?.id === policy.id ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                        Loading...
                      </>
                    ) : (
                      'Select Policy'
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message - Only show if there's an error */}
      {searchClicked && error && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Selected Policy Details */}
      {selectedPolicy && policyDetails && (
        <div className="space-y-8">
          <div className="flex items-center justify-between pb-3 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Complete Policy Details
            </h2>
            {/* Show back button only if there were multiple policies to choose from */}
            {currentCustomerData && currentCustomerData.details["Available Policies"].length > 1 && (
              <button
                onClick={() => {
                  setSelectedPolicy(null);
                  setPolicyDetails(null);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <ArrowRight className="w-4 h-4 rotate-180" />
                Back to Policy Selection
              </button>
            )}
          </div>

          {/* Policy Summary Card */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Policy Summary</h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                policyDetails.data?.POLICY_STATUS === 'Completed'
                  ? 'bg-green-100 text-green-800'
                  : policyDetails.data?.POLICY_STATUS === 'Failed'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {policyDetails.data?.POLICY_STATUS}
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-600">Policy Holder</p>
                <p className="text-lg font-semibold text-gray-900">
                  {policyDetails.data?.CUSTOMER_FIRST_NAME} {policyDetails.data?.CUSTOMER_LAST_NAME}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Policy Type</p>
                <p className="text-lg font-semibold text-gray-900">{policyDetails.data?.POLICY_TYPE}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Face Amount</p>
                <p className="text-lg font-semibold text-green-600">
                  ${policyDetails.data?.FACE_AMOUNT?.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Premium</p>
                <p className="text-lg font-semibold text-gray-900">
                  ${policyDetails.data?.PREMIUM_AMOUNT?.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Customer and Policy Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Customer Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <User className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Customer Information</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Full Name</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.SALUTATION} {policyDetails.data?.CUSTOMER_FIRST_NAME} {policyDetails.data?.CUSTOMER_MIDDLE_NAME} {policyDetails.data?.CUSTOMER_LAST_NAME}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Date of Birth</span>
                  <span className="text-sm text-gray-900">{formatDate(policyDetails.data?.DATE_OF_BIRTH)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Gender</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.GENDER}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Customer ID</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.CUSTOMER_ID}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Email</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.EMAIL}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Phone</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.CONTACT_NUMBER}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Address</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.CUSTOMER_ADDRESS_LINE_1}
                    {policyDetails.data?.CUSTOMER_ADDRESS_LINE_2 && `, ${policyDetails.data?.CUSTOMER_ADDRESS_LINE_2}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">City, State</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.CUSTOMER_CITY}, {policyDetails.data?.CUSTOMER_STATE} {policyDetails.data?.CUSTOMER_ZIP}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Country</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.CUSTOMER_COUNTRY}</span>
                </div>
              </div>
            </div>

            {/* Policy Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Policy Information</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Policy ID</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.POLICY_ID}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Policy Type</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.POLICY_TYPE}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Status</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    policyDetails.data?.POLICY_STATUS === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {policyDetails.data?.POLICY_STATUS}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Issue Date</span>
                  <span className="text-sm text-gray-900">{formatDate(policyDetails.data?.POLICY_ISSUED_DATE)}</span>
                </div>
                {policyDetails.data?.POLICY_EXPIRY_DATE && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600">Expiry Date</span>
                    <span className="text-sm text-gray-900">{formatDate(policyDetails.data.POLICY_EXPIRY_DATE)}</span>
                  </div>
                )}
                {policyDetails.data?.POLICY_TERM_YEARS !== undefined && policyDetails.data?.POLICY_TERM_YEARS !== null && (
                  <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Policy Term</span>
                  <span className="text-sm text-gray-900">{policyDetails.data.POLICY_TERM_YEARS} years</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Agent and Company Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Agent Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <User className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Agent Information</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Agent Name</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.AGENT_SALUTATION} {policyDetails.data?.AGENT_FIRST_NAME} {policyDetails.data?.AGENT_LAST_NAME}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Agent Code</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.AGENT_CODE}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Email</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.AGENT_EMAIL}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Phone</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.AGENT_PHONE_NUMBER}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">State</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.AGENT_STATE}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Status</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    policyDetails.data?.AGENT_STATUS === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {policyDetails.data?.AGENT_STATUS}
                  </span>
                </div>
              </div>
            </div>

            {/* Company Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Insurance Company</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Company Name</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.INSURANCE_COMPANY_NAME}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Phone</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.COMPANY_PHONE}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Email</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.COMPANY_EMAIL}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Address</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.COMPANY_ADDRESS_LINE_1}
                    {policyDetails.data?.COMPANY_ADDRESS_LINE_2 && `, ${policyDetails.data?.COMPANY_ADDRESS_LINE_2}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">City, State</span>
                  <span className="text-sm text-gray-900">
                    {policyDetails.data?.COMPANY_CITY}, {policyDetails.data?.COMPANY_STATE} {policyDetails.data?.COMPANY_ZIP}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Country</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.COMPANY_COUNTRY}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Product Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Product Name</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.INSURANCE_PRODUCT_NAME}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Product Code</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.INSURANCE_PRODUCT_CODE}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Line of Business</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.INSURANCE_PRODUCT_LINE_OF_BUSINESS}</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Status</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    policyDetails.data?.INSURANCE_PRODUCT_STATUS === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {policyDetails.data?.INSURANCE_PRODUCT_STATUS}
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm font-medium text-gray-600">Description</span>
              <p className="text-sm text-gray-900 mt-1">{policyDetails.data?.INSURANCE_PRODUCT_DESCRIPTION}</p>
            </div>
          </div>

          {/* Financial Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Financial Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <DollarSign className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Financial Details</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Face Amount</span>
                  <span className="text-sm text-gray-900 font-semibold text-green-600">
                    ${policyDetails.data?.FACE_AMOUNT?.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Premium Amount</span>
                  <span className="text-sm text-gray-900">${policyDetails.data?.PREMIUM_AMOUNT?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Current Cash Value</span>
                  <span className="text-sm text-gray-900">${policyDetails.data?.CURRENT_CASH_VALUE?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Loan Amount Disbursed</span>
                  <span className="text-sm text-gray-900">${policyDetails.data?.LOAN_AMOUNT_DISBURESED?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Withdrawal Amount</span>
                  <span className="text-sm text-gray-900">${policyDetails.data?.WITHDRAWL_AMOUNT?.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Interest Rates */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-4">
                <Activity className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Interest Rates</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Current Rate</span>
                  <span className="text-sm text-gray-900 font-semibold text-green-600">
                    {policyDetails.data?.CURRENT_INTEREST_RATE_IN_PERCENTAGE}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Guaranteed Rate</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Minimum Rate</span>
                  <span className="text-sm text-gray-900">{policyDetails.data?.MINIMUM_INTEREST_RATE_IN_PERCENTAGE}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Rider Applicable</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    policyDetails.data?.RIDER_APPLICABLE === 1
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {policyDetails.data?.RIDER_APPLICABLE === 1 ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Riders and Beneficiaries */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Active Riders */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-bold text-black">Active Riders</h3>
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Rider Name</th>
                      <th className="px-6 py-3 text-right text-xs font-semibold text-gray-900 uppercase">Coverage</th>
                      <th className="px-6 py-3 text-right text-xs font-semibold text-gray-900 uppercase">Premium</th>
                      <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {policyDetails.data?.riders?.length > 0 ? (
                      policyDetails.data.riders.map((rider, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.RIDER_NAME}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                            ${rider.RIDER_COVERAGE_AMOUNT?.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                            ${rider.RIDER_PREMIUM_AMOUNT?.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              rider.RIDER_STATUS === 'Active'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {rider.RIDER_STATUS}
                            </span>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                          No active riders found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Beneficiaries */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <User className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-bold text-black">Beneficiaries</h3>
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Relationship</th>
                      <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Gender</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {policyDetails.data?.beneficiaries?.length > 0 ? (
                      policyDetails.data.beneficiaries.map((beneficiary, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {beneficiary.SALUTATION} {beneficiary.BENEFICIARY_FIRST_NAME} {beneficiary.BENEFICIARY_LAST_NAME}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{beneficiary.BENEFICIARY_RELATIONSHIP}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{beneficiary.BENEFICIARY_GENDER}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                          No beneficiaries found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Transaction History</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Transaction ID</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {policyDetails.data?.transactions?.length > 0 ? (
                    policyDetails.data.transactions.map((transaction, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.TRANSACTION_ID}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatDate(transaction.TRANSACTION_DATE)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.TRANSACTION_TYPE}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.TRANSACTION_AMOUNT}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              transaction.TRANSACTION_STATUS === 'Completed'
                                ? 'bg-green-100 text-green-800'
                                : transaction.TRANSACTION_STATUS === 'Failed'
                                ? 'bg-red-100 text-red-800'
                                : transaction.TRANSACTION_STATUS === 'Pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {transaction.TRANSACTION_STATUS}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                        No transactions found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Loans and Repayments */}
          {policyDetails.data?.loans?.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-bold text-black">Policy Loans</h3>
                </div>
              </div>
              <div className="p-6">
                {policyDetails.data.loans.map((loan, loanIndex) => (
                  <div key={loanIndex} className="mb-6 last:mb-0">
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <span className="text-sm font-medium text-gray-600">Loan Amount</span>
                          <p className="text-lg font-semibold text-gray-900">${loan.LOAN_AMOUNT?.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Interest Rate</span>
                          <p className="text-lg font-semibold text-gray-900">{loan.LOAN_INTEREST_RATE_IN_PERCENTAGE}%</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Status</span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            loan.LOAN_STATUS === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {loan.LOAN_STATUS}
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <span className="text-sm font-medium text-gray-600">Issue Date</span>
                          <p className="text-sm text-gray-900">{formatDate(loan.LOAN_ISSUED_DATE)}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Period</span>
                          <p className="text-sm text-gray-900">{loan.LOAN_PERIOD_IN_MONTHS} months</p>
                        </div>
                      </div>
                    </div>

                    {/* Repayment Schedule */}
                    {loan.repayments?.length > 0 && (
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 mb-3">Repayment History</h4>
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-900 uppercase">Due Date</th>
                                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-900 uppercase">Paid Date</th>
                                <th className="px-4 py-2 text-right text-xs font-semibold text-gray-900 uppercase">Amount</th>
                                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                              {loan.repayments.map((repayment, repayIndex) => (
                                <tr key={repayIndex}>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{formatDate(repayment.LOAN_SCHEDULED_DUE_DATE)}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{formatDate(repayment.LOAN_ACTUAL_REPAYMENT_DATE)}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 text-right">
                                    ${repayment.LOAN_REPAYMENT_AMOUNT?.toLocaleString()}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      repayment.STATUS === 'Paid'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-yellow-100 text-red-800'
                                    }`}>
                                      {repayment.STATUS}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Proceed to Illustration Button */}
          <div className="text-center">
            <button
              className="bg-green-600 text-white px-8 py-3 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center gap-2 mx-auto disabled:bg-gray-400 disabled:cursor-not-allowed"
              disabled={isLoading}
              onClick={async () => {
                if (selectedPolicy && currentCustomerData) {
                  try {
                    // 🚀 BACKEND API CALL: Fetch allowed illustration types
                    const policyId = parseInt(selectedPolicy.id.toString()) || 0;
                    const customerIdNum = parseInt(currentCustomerData.customerId) || 0;
                    const policyType = selectedPolicy.name || selectedPolicy.description || 'Unknown';

                    console.log('Calling backend API for illustration types:', { policyId, customerIdNum, policyType });

                    // Call backend to get allowed illustration types - YOU WILL REPLACE THIS URL
                    if (policyId > 0 && customerIdNum > 0) {
                      await fetchAllowedIllustrationTypes(policyId, customerIdNum, policyType);
                    } else {
                      console.warn('Invalid policy or customer ID, skipping API call');
                    }

                    // Use complete customer information if available, otherwise use current data
                    let customerName = currentCustomerData.name;
                    let policyNumber = currentCustomerData.policyNumber;
                    let customerId = currentCustomerData.customerId;
                    let premiumAmount = selectedPolicy.premium;

                    // If we have complete policy details, use the more detailed information
                    if (policyDetails && policyDetails.data) {
                      const fullName = `${policyDetails.data.CUSTOMER_FIRST_NAME || ''} ${policyDetails.data.CUSTOMER_MIDDLE_NAME || ''} ${policyDetails.data.CUSTOMER_LAST_NAME || ''}`.trim();
                      customerName = fullName || currentCustomerData.name;
                      policyNumber = policyDetails.data.POLICY_NUMBER || currentCustomerData.policyNumber;
                      customerId = policyDetails.data.CUSTOMER_ID?.toString() || currentCustomerData.customerId;
                      // ✅ Use actual premium amount from complete policy details
                      premiumAmount = policyDetails.data.PREMIUM_AMOUNT ? `$${policyDetails.data.PREMIUM_AMOUNT.toLocaleString()} annually` : selectedPolicy.premium;
                    }

                    // Save selected customer and policy data to context
                    setSelectedCustomerData({
                      name: customerName,
                      policyNumber: policyNumber,
                      customerId: customerId,
                      details: {
                        ...currentCustomerData.details,
                        // Override with complete policy details if available
                        ...(policyDetails && policyDetails.data && {
                          DOB: formatDate(policyDetails.data.DATE_OF_BIRTH) || currentCustomerData.details.DOB,
                          Email: policyDetails.data.EMAIL || currentCustomerData.details.Email,
                          Phone: policyDetails.data.CONTACT_NUMBER || currentCustomerData.details.Phone,
                          Address: `${policyDetails.data.CUSTOMER_ADDRESS_LINE_1 || ''} ${policyDetails.data.CUSTOMER_ADDRESS_LINE_2 || ''}`.trim() || currentCustomerData.details.Address
                        })
                      }
                    });

                    setSelectedPolicyData({
                      id: selectedPolicy.id,
                      name: selectedPolicy.name,
                      description: selectedPolicy.description,
                      coverage: selectedPolicy.coverage,
                      premium: premiumAmount, // ✅ Now uses actual premium from backend
                      features: selectedPolicy.features,
                      // Add complete policy details
                      ...(policyDetails && policyDetails.data && {
                        issueDate: formatDate(policyDetails.data.POLICY_ISSUED_DATE),
                        expiryDate: formatDate(policyDetails.data.POLICY_EXPIRY_DATE),
                        policyStartDate: formatDate(policyDetails.data.POLICY_ISSUED_DATE),
                        faceAmount: policyDetails.data.FACE_AMOUNT,
                        premiumAmount: policyDetails.data.PREMIUM_AMOUNT,
                        currentCashValue: policyDetails.data.CURRENT_CASH_VALUE,
                        policyTermYears: policyDetails.data.POLICY_TERM_YEARS,
                        CURRENT_INTEREST_RATE_IN_PERCENTAGE: policyDetails.data.CURRENT_INTEREST_RATE_IN_PERCENTAGE,
                        GUARANTEED_INTEREST_RATE_IN_PERCENTAGE: policyDetails.data.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE
                      })
                    });

                    setActiveTab('as-is');
                  } catch (error) {
                    console.error('Error fetching allowed illustration types:', error);
                    // Continue to illustration page even if API call fails
                    setActiveTab('as-is');
                  }
                }
              }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Loading Illustration Types...
                </>
              ) : (
                <>
                  <Calculator className="w-5 h-5" />
                  Go to Illustration
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PolicySelection;
