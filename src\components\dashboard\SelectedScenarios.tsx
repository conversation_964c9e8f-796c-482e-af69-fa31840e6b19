import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, FileText, Info, X, Alert<PERSON>riangle, Trash2 } from 'lucide-react';
import { <PERSON><PERSON>hart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart as RechartsBarChart, Bar, AreaChart as RechartsAreaChart, Area, PieChart as RechartsPieChart, Cell, Pie } from 'recharts';
import Card from '../common/Card';
import Button from '../common/Button';
import LoadingSpinner from '../common/LoadingSpinner';
import { useDashboard } from '../../contexts/DashboardContext';
import type { Scenario as BaseScenario } from '../../types';
import {
  initializeDisclosureData,
  type DisclosureItem
} from '../../services/disclosureService';
import {
  prepareChartData,
  preparePieChartData,
  calculateKeyMetrics,
  type ScenarioTableData
} from '../../services/scenarioService';
import {
  generateAsIsLoanTableData,
  generatePremiumAnalysisTableData,
  generateDeathBenefitTableData,
  generatePolicyPerformanceTableData,
  generateRiskAssessmentTableData,
  AsIsLoan,
  FaceAmount,
  FaceAmountVariesByYear,
  PremiumAmount,
  StopPremiumAmount,
  type AsIsLoanTableData,
  type FaceAmountTableData,
  type FaceAmountVariesByYearTableData,
  type PremiumAmountTableData,
  type StopPremiumAmountTableData
} from '../../data/mockScenarioData';

type ScenarioWithKeyPoints = BaseScenario & { keyPoints?: string[] };

// Function to get detailed table configuration and data based on scenario type
const getDetailedTableConfig = (scenario: any) => {
  console.log('📊 Getting detailed table config for scenario:', scenario);

  // Determine table type based on scenario name and category
  const scenarioName = scenario.name?.toLowerCase() || '';
  const scenarioCategory = scenario.category || '';

  // As-Is scenarios
  if (scenarioCategory === 'as-is' || scenarioName.includes('as-is')) {
    return {
      columns: AsIsLoan,
      data: generateAsIsLoanTableData(),
      title: 'As-Is Cash Value Analysis'
    };
  }

  // Face Amount scenarios
  if (scenarioCategory === 'face-amount' || scenarioName.includes('face amount')) {
    // Check if it's modify by year scenario
    if (scenarioName.includes('modify') || scenarioName.includes('by year') || scenarioName.includes('varies')) {
      return {
        columns: FaceAmountVariesByYear,
        data: generatePolicyPerformanceTableData(),
        title: 'Face Amount Varies By Year Analysis'
      };
    } else {
      return {
        columns: FaceAmount,
        data: generateDeathBenefitTableData(),
        title: 'Face Amount Analysis'
      };
    }
  }

  // Premium scenarios
  if (scenarioCategory === 'premium' || scenarioName.includes('premium')) {
    // Check if it's stop premium scenario
    if (scenarioName.includes('stop') || scenarioName.includes('cease') || scenarioName.includes('future premium')) {
      return {
        columns: StopPremiumAmount,
        data: generateRiskAssessmentTableData(),
        title: 'Stop Premium Analysis'
      };
    } else {
      return {
        columns: PremiumAmount,
        data: generatePremiumAnalysisTableData(),
        title: 'Premium Amount Analysis'
      };
    }
  }

  // Default to As-Is table
  return {
    columns: AsIsLoan,
    data: generateAsIsLoanTableData(),
    title: 'Default Analysis'
  };
};

// Function to get hardcoded table data based on scenario
const getHardcodedTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Getting hardcoded table data for scenario:', scenario);

  // Map scenario categories to appropriate data functions
  const scenarioId = parseInt(scenario.id);

  let rawData: any[] = [];

  // Determine which hardcoded data to use based on scenario category or ID
  if (scenario.category === 'as-is' || scenarioId === 1) {
    rawData = generateAsIsLoanTableData();
  } else if (scenario.category === 'premium' || scenarioId === 2) {
    rawData = generatePremiumAnalysisTableData();
  } else if (scenario.category === 'face-amount' || scenarioId === 3) {
    rawData = generateDeathBenefitTableData();
  } else if (scenario.category === 'policy-performance' || scenarioId === 4) {
    rawData = generatePolicyPerformanceTableData();
  } else if (scenario.category === 'risk-assessment' || scenarioId === 5) {
    rawData = generateRiskAssessmentTableData();
  } else {
    // Default to As-Is data
    rawData = generateAsIsLoanTableData();
  }

  // Convert the detailed hardcoded data to the simplified ScenarioTableData format
  const convertedData: ScenarioTableData[] = rawData.map(row => ({
    policyYear: row.policyYear,
    endOfAge: row.age,
    plannedPremium: row.plannedPremium,
    netOutlay: row.plannedPremium * row.policyYear, // Simple calculation for net outlay
    netSurrenderValue: row.netCashValue || row.netValueBeginningOfYear || 0,
    netDeathBenefit: row.faceAmount || 350000 // Use face amount or default
  }));

  console.log('✅ Converted hardcoded data:', {
    originalRows: rawData.length,
    convertedRows: convertedData.length,
    firstRow: convertedData[0],
    lastRow: convertedData[convertedData.length - 1]
  });

  return convertedData;
};

const SelectedScenarios: React.FC = () => {
  const { scenarios, selectedCustomerData, selectedPolicyData, setActiveTab, deleteScenario } = useDashboard();
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showVisualization, setShowVisualization] = useState(false);

  const [showDisclosurePopup, setShowDisclosurePopup] = useState(false);
  const [selectedCharts, setSelectedCharts] = useState({
    line: true,
    bar: true,
    area: true,
    pie: true
  });

  // Disclosure data state (only disclosure uses backend)
  const [disclosureData, setDisclosureData] = useState<DisclosureItem[]>([]);
  const [loadingDisclosure, setLoadingDisclosure] = useState(false);
  const [disclosureError, setDisclosureError] = useState<string | null>(null);

  // Table data state for selected scenario (using hardcoded data)
  const [scenarioTableData, setScenarioTableData] = useState<ScenarioTableData[]>([]);

  // Detailed table data state for full table display
  const [detailedTableData, setDetailedTableData] = useState<any[]>([]);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // Chart colors
  const chartColors = {
    plannedPremium: '#3B82F6',
    netOutlay: '#10B981',
    netSurrenderValue: '#8B5CF6',
    netDeathBenefit: '#F59E0B'
  }
  // Table data will be fetched from backend when scenario details are implemented

  // Show all scenarios saved in current session (fresh start approach)
  const displayScenarios: ScenarioWithKeyPoints[] = (scenarios as ScenarioWithKeyPoints[]).filter(scenario => {
    // Show all scenarios that were saved in the current session
    console.log('🔍 Displaying scenario:', scenario.id, 'category:', scenario.category, 'name:', scenario.name);
    return true; // Show all current session scenarios
  });

  // Debug logging
  console.log('🔍 SelectedScenarios render - scenarios:', scenarios.length, scenarios);
  console.log('🔍 SelectedScenarios render - displayScenarios:', displayScenarios.length, displayScenarios);
  console.log('🔍 SelectedScenarios render - selectedPolicyData:', selectedPolicyData?.id);

  const handleScenarioClick = (scenarioId: string) => {
    setSelectedScenario(selectedScenario === scenarioId ? null : scenarioId);
  };

  const handleDeleteScenario = async (scenarioId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click

    if (window.confirm('Are you sure you want to delete this scenario? This action cannot be undone.')) {
      try {
        await deleteScenario(scenarioId);
        // If the deleted scenario was currently selected, clear the selection
        if (selectedScenario === scenarioId) {
          setSelectedScenario(null);
        }
      } catch (error) {
        console.error('Failed to delete scenario:', error);
        alert('Failed to delete scenario. Please try again.');
      }
    }
  };



  // Fetch disclosure data when component mounts or policy data changes
  // DO NOT LOAD OLD SCENARIOS - Always start fresh
  useEffect(() => {
    const fetchDataForPolicy = async () => {
      // DO NOT LOAD OLD SCENARIOS - Always start fresh after login
      console.log('🚫 SelectedScenarios: NOT loading old scenarios for fresh start');
      console.log('🔄 Policy selected but keeping empty scenarios for fresh illustration experience');

      // Use the service method to initialize disclosure data
      await initializeDisclosureData(
        selectedCustomerData,
        selectedPolicyData,
        setLoadingDisclosure,
        setDisclosureError,
        setDisclosureData
      );
    };

    fetchDataForPolicy();
  }, [selectedCustomerData?.customerId, selectedPolicyData?.id]);

  // Load table data when scenario is selected (using hardcoded data)
  useEffect(() => {
    const loadTableData = () => {
      if (!selectedScenario || !selectedPolicyData) {
        setScenarioTableData([]);
        return;
      }

      const scenario = displayScenarios.find(s => s.id === selectedScenario);
      if (!scenario) {
        console.warn('⚠️ Selected scenario not found in displayScenarios');
        setScenarioTableData([]);
        return;
      }

      console.log('🔄 Loading hardcoded table data for scenario:', {
        scenarioId: selectedScenario,
        policyId: selectedPolicyData.id,
        scenarioName: scenario.name,
        scenarioCategory: scenario.category
      });

      try {
        // Use hardcoded data instead of database - load immediately without loading state
        const tableData = getHardcodedTableData(scenario);

        // Get detailed table configuration and data
        const detailedConfig = getDetailedTableConfig(scenario);

        console.log('✅ Hardcoded table data loaded instantly:', {
          rowCount: tableData.length,
          firstRow: tableData[0],
          lastRow: tableData[tableData.length - 1],
          detailedRowCount: detailedConfig.data.length,
          detailedColumns: detailedConfig.columns.length,
          tableTitle: detailedConfig.title
        });

        setScenarioTableData(tableData);
        setDetailedTableData(detailedConfig.data);
        setTableColumns(detailedConfig.columns);
      } catch (error) {
        console.error('❌ Error loading hardcoded table data:', error);
        setScenarioTableData([]);
        setDetailedTableData([]);
        setTableColumns([]);
      }
    };

    loadTableData();
  }, [selectedScenario, displayScenarios, selectedPolicyData?.id]);



  // Custom tooltip formatter
  const formatTooltip = (value: any, name: string) => {
    return [`$${value.toLocaleString()}`, name];
  };

  // Handle chart selection toggle
  const toggleChart = (chartType: keyof typeof selectedCharts) => {
    setSelectedCharts(prev => ({
      ...prev,
      [chartType]: !prev[chartType]
    }));
  };

  // Select all charts
  const selectAllCharts = () => {
    setSelectedCharts({
      line: true,
      bar: true,
      area: true,
      pie: true
    });
  };

  // Clear all charts
  const clearAllCharts = () => {
    setSelectedCharts({
      line: false,
      bar: false,
      area: false,
      pie: false
    });
  };

  // Individual chart rendering functions
  const renderLineChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsLineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="Planned Premium"
            stroke={chartColors.plannedPremium}
            strokeWidth={3}
            dot={{ fill: chartColors.plannedPremium, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.plannedPremium, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Outlay"
            stroke={chartColors.netOutlay}
            strokeWidth={3}
            dot={{ fill: chartColors.netOutlay, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netOutlay, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Surrender Value"
            stroke={chartColors.netSurrenderValue}
            strokeWidth={3}
            dot={{ fill: chartColors.netSurrenderValue, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netSurrenderValue, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Death Benefit"
            stroke={chartColors.netDeathBenefit}
            strokeWidth={3}
            dot={{ fill: chartColors.netDeathBenefit, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netDeathBenefit, strokeWidth: 2 }}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    );
  };

  const renderBarChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Bar dataKey="Planned Premium" fill={chartColors.plannedPremium} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Outlay" fill={chartColors.netOutlay} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Surrender Value" fill={chartColors.netSurrenderValue} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Death Benefit" fill={chartColors.netDeathBenefit} radius={[2, 2, 0, 0]} />
        </RechartsBarChart>
      </ResponsiveContainer>
    );
  };

  const renderAreaChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsAreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Area
            type="monotone"
            dataKey="Net Death Benefit"
            stackId="1"
            stroke={chartColors.netDeathBenefit}
            fill={chartColors.netDeathBenefit}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Net Surrender Value"
            stackId="1"
            stroke={chartColors.netSurrenderValue}
            fill={chartColors.netSurrenderValue}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Net Outlay"
            stackId="1"
            stroke={chartColors.netOutlay}
            fill={chartColors.netOutlay}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Planned Premium"
            stackId="1"
            stroke={chartColors.plannedPremium}
            fill={chartColors.plannedPremium}
            fillOpacity={0.6}
          />
        </RechartsAreaChart>
      </ResponsiveContainer>
    );
  };

  const renderPieChart = (pieData: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsPieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(1)}%`}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
          >
            {pieData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => `$${value.toLocaleString()}`} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    );
  };







  const selectedScenarioData = selectedScenario ? 
    displayScenarios.find(s => s.id === selectedScenario) : null;



  return (
    <div className="space-y-6 px-6">
      {/* Current Policy Information Card - Only Show After Policy Selection */}
      {selectedPolicyData && (
        /* Current Policy Information Card - Matching Image Structure */
        <Card className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Current Policy Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Number</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.policyNumber || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer Name</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.name || 'Sophia A. Miller'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer ID</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.customerId || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Date of Birth</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.details?.DOB || '1990-05-15'}
                </p>
              </div>
            </div>
            
            {/* Second Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Type</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedPolicyData?.name || 'Whole Life'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Face Amount</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  $ {selectedPolicyData?.coverage ? parseInt(selectedPolicyData.coverage).toLocaleString() : '250000'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Annual Premium</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedPolicyData?.premium || '1500'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Current Policy Year</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  Year 6
                </p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Action Buttons - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <div className="flex justify-end space-x-4">
          <Button
            variant="secondary"
            onClick={() => setShowDisclosurePopup(true)}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
          >
            <Info className="w-5 h-5" />
            <span>Disclosure</span>
          </Button>

          <Button
            variant="primary"
            onClick={() => setActiveTab('analysis-reports')}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            <FileText className="w-5 h-5" />
            <span>Get Illustration Reports</span>
          </Button>
        </div>
      )}

      {/* No Scenarios Saved Message - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length === 0 && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Scenarios Saved</h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                No scenarios have been saved yet. Go to Policy Illustration to create and select scenarios.
              </p>
              <Button
                variant="primary"
                onClick={() => setActiveTab('illustrations')}
              >
                Policy Illustration
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Policy Selection Required Message */}
      {!selectedPolicyData && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">Policy Selection Required</h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                Please select a policy first to view your selected scenarios and illustrations.
              </p>
              <Button
                variant="primary"
                onClick={() => setActiveTab('policy-selection')}
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Grid - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayScenarios.map((scenario) => (
            <div
              key={scenario.id}
              onClick={() => handleScenarioClick(scenario.id)}
              className={`
                bg-white dark:bg-gray-800 rounded-lg border p-5 cursor-pointer transition-all duration-200 relative
                ${selectedScenario === scenario.id
                  ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-lg ring-2 ring-blue-200 dark:ring-blue-800'
                  : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-sm'
                }
              `}
            >
              {/* Delete Button */}
              <button
                onClick={(e) => handleDeleteScenario(scenario.id, e)}
                className="absolute top-3 right-3 p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                title="Delete scenario"
              >
                <Trash2 className="w-4 h-4" />
              </button>

              {/* Simple Title */}
              <div className="mb-4 pr-8">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900 dark:text-white text-lg">
                    {scenario.name || (() => {
                      switch(scenario.category) {
                        case 'as-is': return 'AS-IS Illustrations';
                        case 'face-amount': return 'Face Amount Illustrations';
                        case 'premium': return 'Premium Illustrations';
                        case 'income': return 'Income Illustrations';
                        case 'loan-repayment': return 'Loan Repayment Illustrations';
                        case 'interest-rate': return 'Interest Rate Illustrations';
                        case 'policy-lapse': return 'Policy Lapse Illustrations';
                        default: return 'Scenario Illustrations';
                      }
                    })()}
                  </h4>
                  {/* Selected Indicator */}
                  {selectedScenario === scenario.id && (
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full">
                        SELECTED
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Question */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">Question</p>
                <p className="text-sm text-gray-900 dark:text-white font-medium">
                  {scenario.asIsDetails || 'Scenario analysis question'}
                </p>
              </div>

              {/* Selected Options with Values */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">Selected Options</p>
                <div className="space-y-1">
                  {scenario.keyPoints && scenario.keyPoints.length > 0 ? (
                    scenario.keyPoints
                      .filter(point => point.startsWith('Selected Option:')) // Only show selected options
                      .map((point, index) => (
                        <p key={index} className="text-sm text-gray-900 dark:text-white">
                          {point.replace('Selected Option: ', '')} {/* Remove prefix for cleaner display */}
                        </p>
                      ))
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      No options selected
                    </p>
                  )}

                  {/* Fallback to whatIfOptions if no selected options in keyPoints */}
                  {(!scenario.keyPoints || !scenario.keyPoints.some(point => point.startsWith('Selected Option:'))) &&
                   scenario.whatIfOptions && scenario.whatIfOptions.length > 0 && (
                    <p className="text-sm text-gray-900 dark:text-white font-medium">
                      {scenario.whatIfOptions.join(', ')}
                    </p>
                  )}
                </div>
              </div>

              {/* View Button */}
              <div className="text-center pt-3 border-t border-gray-200 dark:border-gray-600">
                <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">View Details</span>
              </div>
            </div>
          ))}
        </div>
      )}



      {/* Inline Scenario Details and Table */}
      {selectedScenario && selectedScenarioData && (
        <div className="mt-8 space-y-6">




          {/* Key Performance Metrics */}
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Key Performance Metrics</h3>
            {(() => {
              if (scenarioTableData.length === 0) {
                return (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                    <div className="text-center">
                      <Info className="w-12 h-12 text-yellow-600 dark:text-yellow-400 mx-auto mb-3" />
                      <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">No Metrics Available</h4>
                      <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                        Click on a scenario card to view key performance metrics.
                      </p>
                    </div>
                  </div>
                );
              }

              // Use the service method to calculate metrics from hardcoded data
              const keyMetrics = calculateKeyMetrics(scenarioTableData);

              return (
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      ${keyMetrics.totalPremiums.toLocaleString()}
                    </div>
                    <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Total Premiums</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/30 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      ${keyMetrics.totalOutlay.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-700 dark:text-green-300 font-medium">Total Outlay</div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      ${keyMetrics.finalSurrenderValue.toLocaleString()}
                    </div>
                    <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">Final Cash Value</div>
                  </div>
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/30 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                    <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      ${keyMetrics.finalDeathBenefit.toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Final Death Benefit</div>
                  </div>
                  <div className={`bg-gradient-to-br p-4 rounded-lg border ${
                    keyMetrics.netGain >= 0
                      ? 'from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/30 border-emerald-200 dark:border-emerald-700'
                      : 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/30 border-red-200 dark:border-red-700'
                  }`}>
                    <div className={`text-2xl font-bold ${
                      keyMetrics.netGain >= 0
                        ? 'text-emerald-600 dark:text-emerald-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      ${keyMetrics.netGain.toLocaleString()}
                    </div>
                    <div className={`text-sm font-medium ${
                      keyMetrics.netGain >= 0
                        ? 'text-emerald-700 dark:text-emerald-300'
                        : 'text-red-700 dark:text-red-300'
                    }`}>
                      Net {keyMetrics.netGain >= 0 ? 'Gain' : 'Loss'}
                    </div>
                  </div>
                  <div className={`bg-gradient-to-br p-4 rounded-lg border ${
                    keyMetrics.roi >= 0
                      ? 'from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/30 border-teal-200 dark:border-teal-700'
                      : 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/30 border-red-200 dark:border-red-700'
                  }`}>
                    <div className={`text-2xl font-bold ${
                      keyMetrics.roi >= 0
                        ? 'text-teal-600 dark:text-teal-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {keyMetrics.roi.toFixed(1)}%
                    </div>
                    <div className={`text-sm font-medium ${
                      keyMetrics.roi >= 0
                        ? 'text-teal-700 dark:text-teal-300'
                        : 'text-red-700 dark:text-red-300'
                    }`}>
                      ROI
                    </div>
                  </div>
                </div>
              );
            })()}
          </Card>

          {/* Enhanced Detailed Illustration Table */}
          <Card>
            <div className="flex items-center justify-between mb-6">
              <div className="flex flex-col">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                  <BarChart3 className="w-6 h-6 text-blue-600 mr-2" />
                  {(() => {
                    const scenario = displayScenarios.find(s => s.id === selectedScenario);
                    if (scenario) {
                      const config = getDetailedTableConfig(scenario);
                      return config.title;
                    }
                    return 'Detailed Analysis';
                  })()}
                </h3>
                {/* Scenario Indicator */}
                {(() => {
                  const scenario = displayScenarios.find(s => s.id === selectedScenario);
                  if (scenario) {
                    return (
                      <div className="flex items-center mt-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Displaying data for: <span className="font-medium text-gray-900 dark:text-white">{scenario.name || 'Selected Scenario'}</span>
                        </span>
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {detailedTableData.length > 0 ? `${detailedTableData.length} Rows • ${tableColumns.length} Columns • Showing first 25 rows` : 'Loading...'}
              </div>
            </div>

            {/* Scrollable Table Container */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              {detailedTableData.length > 0 && tableColumns.length > 0 ? (
                <div className="overflow-auto" style={{ maxHeight: '600px' }}>
                  <table className="w-full min-w-max">
                    <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 sticky top-0 z-10">
                      <tr>
                        <th className="px-3 py-3 text-left font-bold text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-700">#</th>
                        {tableColumns.map((column, index) => (
                          <th
                            key={index}
                            className="px-3 py-3 text-left font-bold text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 whitespace-nowrap"
                            style={{ minWidth: `${column.width || 120}px` }}
                          >
                            {column.header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {detailedTableData.slice(0, 25).map((row, rowIndex) => (
                        <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <td className="px-3 py-2 text-gray-700 dark:text-gray-200 font-medium text-xs border-r border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 sticky left-0">
                            {rowIndex + 1}
                          </td>
                          {tableColumns.map((column, colIndex) => (
                            <td
                              key={colIndex}
                              className="px-3 py-2 text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 whitespace-nowrap"
                            >
                              {column.isCurrency && row[column.key] !== null && row[column.key] !== undefined
                                ? `$${Number(row[column.key]).toLocaleString()}`
                                : row[column.key] !== null && row[column.key] !== undefined
                                ? row[column.key]
                                : '-'
                              }
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="p-8 text-center">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">No detailed table data available</p>
                </div>
              )}

              {/* Table Info Footer */}
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {detailedTableData.length > 25 ? (
                    <>
                      Showing first 25 rows of {detailedTableData.length} total rows.
                      <span className="text-blue-600 dark:text-blue-400 ml-1">Scroll vertically and horizontally to view all data.</span>
                    </>
                  ) : (
                    <>
                      Showing all {detailedTableData.length} rows.
                      <span className="text-blue-600 dark:text-blue-400 ml-1">Scroll horizontally to view all columns.</span>
                    </>
                  )}
                </p>
              </div>
            </div>
          </Card>

          {/* Data Visualization Options */}
            <Card>
            <div className="flex items-center justify-between">
              <div /> {/* Empty div to push button to the right */}
              {/* Data Visualization Button */}
              <button
              onClick={() => setShowVisualization(!showVisualization)}
              className="flex items-center space-x-2 px-5 py-2.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors font-medium"
              >
              <BarChart3 className="w-4 h-4" />
              <span>{showVisualization ? 'Hide Charts' : 'Show Charts'}</span>
              </button>
            </div>
            </Card>



          {/* Visualization Section */}
          {showVisualization && (
            <div className="space-y-6">
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Interactive Data Visualization</h3>

              {/* Chart Selection Checkboxes */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Select Charts to Display:</h4>
                  <div className="flex space-x-2">
                    <button
                      onClick={selectAllCharts}
                      className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      Select All
                    </button>
                    <button
                      onClick={clearAllCharts}
                      className="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.line}
                      onChange={() => toggleChart('line')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <LineChart className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600">Line Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.bar}
                      onChange={() => toggleChart('bar')}
                      className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-green-600">Bar Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.area}
                      onChange={() => toggleChart('area')}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <AreaChart className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-purple-600">Area Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.pie}
                      onChange={() => toggleChart('pie')}
                      className="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <PieChart className="w-4 h-4 text-orange-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-orange-600">Pie Chart</span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Charts Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {(() => {
                  if (scenarioTableData.length === 0) {
                    return (
                      <div className="col-span-full bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-8">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
                          <h4 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-2">No Chart Data Available</h4>
                          <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                            Click on a scenario card to view interactive charts and visualizations.
                          </p>
                        </div>
                      </div>
                    );
                  }

                  const chartData = prepareChartData(scenarioTableData);
                  const pieData = preparePieChartData(scenarioTableData, chartColors);
                  const charts = [];

                  console.log('📊 Preparing charts with hardcoded data:', {
                    tableRows: scenarioTableData.length,
                    chartDataPoints: chartData.length,
                    pieDataPoints: pieData.length
                  });

                  if (selectedCharts.line) {
                    charts.push(
                      <Card key="line" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <LineChart className="w-5 h-5 text-blue-600 mr-2" />
                            Line Chart - Trend Analysis
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Shows the progression of policy values over time with smooth trend lines
                          </p>
                        </div>
                        {renderLineChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.bar) {
                    charts.push(
                      <Card key="bar" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <BarChart3 className="w-5 h-5 text-green-600 mr-2" />
                            Bar Chart - Value Comparison
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Compare different policy values side by side for each year
                          </p>
                        </div>
                        {renderBarChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.area) {
                    charts.push(
                      <Card key="area" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <AreaChart className="w-5 h-5 text-purple-600 mr-2" />
                            Area Chart - Cumulative Growth
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Visualize the cumulative growth and stacking of policy values
                          </p>
                        </div>
                        {renderAreaChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.pie) {
                    charts.push(
                      <Card key="pie" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <PieChart className="w-5 h-5 text-orange-600 mr-2" />
                            Pie Chart - Final Year Distribution
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Distribution of policy values in the final projection year
                          </p>
                        </div>
                        {renderPieChart(pieData)}
                      </Card>
                    );
                  }

                  return charts.length > 0 ? charts : (
                    <div className="col-span-full">
                      <Card className="p-12">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                          <h4 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">No Charts Selected</h4>
                          <p className="text-gray-600 dark:text-gray-400">
                            Please select at least one chart type from the options above to view your data visualization.
                          </p>
                        </div>
                      </Card>
                    </div>
                  );
                })()}
              </div>
            </Card>
            </div>
          )}
        </div>
      )}

      {/* Disclosure Popup Modal */}
      {showDisclosurePopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <Info className="w-5 h-5 text-blue-600 mr-2" />
                Policy Disclosure Information
              </h2>
              <button
                onClick={() => setShowDisclosurePopup(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Modal Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4">
              {loadingDisclosure ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : disclosureError ? (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
                    <span className="text-red-800 dark:text-red-200 font-medium">Error loading disclosures</span>
                  </div>
                  <p className="text-red-700 dark:text-red-300 text-sm mt-1">{disclosureError}</p>
                </div>
              ) : disclosureData.length === 0 ? (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-center">
                    <Info className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                    <span className="text-yellow-800 dark:text-yellow-200 font-medium">No disclosures available</span>
                  </div>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mt-1">
                    There are no disclosures to display for this policy at the moment.
                  </p>
                </div>
              ) : null}
              {loadingDisclosure ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h4 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Loading Disclosure Data</h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Please wait while we fetch the policy disclosure information...
                    </p>
                  </div>
                </div>
              ) : disclosureError ? (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                  <div className="text-center">
                    <AlertTriangle className="w-16 h-16 text-red-600 dark:text-red-400 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">Error Loading Disclosure Data</h4>
                    <p className="text-red-700 dark:text-red-300 mb-4">{disclosureError}</p>
                    <button
                      onClick={async () => {
                        // Use the service method to retry disclosure data fetch
                        await initializeDisclosureData(
                          selectedCustomerData,
                          selectedPolicyData,
                          setLoadingDisclosure,
                          setDisclosureError,
                          setDisclosureData
                        );
                      }}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors font-medium"
                    >
                      Retry
                    </button>
                  </div>
                </div>
              ) : disclosureData.length === 0 ? (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                  <div className="text-center">
                    <Info className="w-16 h-16 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-2">No Disclosure Data Available</h4>
                    <p className="text-yellow-700 dark:text-yellow-300">
                      No disclosure information is available from the backend for this policy.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-gray-700">
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 w-16 text-sm">
                          S.No
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 w-48 text-sm">
                          Type
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 text-sm">
                          Disclosure
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {disclosureData.map((item) => (
                        <tr key={item.DISCLOSURE_ID} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-medium text-gray-900 dark:text-white text-sm">
                            {item.DISCLOSURE_ID}
                          </td>
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-semibold text-gray-900 dark:text-white text-sm">
                            {item.TYPE}
                          </td>
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-justify text-gray-700 dark:text-gray-300 leading-relaxed text-sm">
                            {item.DISCLOSURE}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Modal Footer - Always Visible */}
            <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gray-50 dark:bg-gray-800">
              <Button
                variant="primary"
                onClick={() => setShowDisclosurePopup(false)}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedScenarios;